package so.appio.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.entity.service.Service
import so.appio.app.ui.theme.AppioAppTheme
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun ServiceScreen(
    modifier: Modifier = Modifier,
    service: Service,
) {
    val context = LocalContext.current
    val notificationRepository = remember { DatabaseManager.getNotificationRepository() }
    val notifications by notificationRepository.getNotificationsByService(service.id).collectAsState(initial = emptyList())

    Scaffold(modifier = modifier.fillMaxSize()) { innerPadding ->
        ServiceContent(
            service = service,
            notifications = notifications,
            onRefresh = {
                // TODO: Implement refresh functionality
                println("TODO: Implement notification refresh")
            },
            modifier = Modifier.padding(innerPadding)
        )
    }
}

@Composable
private fun ServiceContent(
    service: Service,
    notifications: List<Notification>,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // Header with service title
        ServiceHeader(service = service)

        // Notifications list
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) {
            items(notifications) { notification ->
                NotificationItem(
                    notification = notification,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }
        }

        // Footer with count and refresh button
        ServiceFooter(
            notificationCount = notifications.size,
            onRefresh = onRefresh
        )
    }
}

@Composable
private fun ServiceHeader(
    service: Service,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = service.title,
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun NotificationItem(
    notification: Notification,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Notification title
            Text(
                text = notification.title,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Spacer(modifier = Modifier.height(4.dp))

            // Received date
            Text(
                text = formatDate(notification.receivedAt),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Notification body (truncated to 1 line)
            Text(
                text = notification.body,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

@Composable
private fun ServiceFooter(
    notificationCount: Int,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = 4.dp
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Empty space for left alignment
            Spacer(modifier = Modifier.weight(1f))

            // Notification count in center
            Text(
                text = "$notificationCount notifications",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f),
                textAlign = TextAlign.Center
            )

            // Refresh button on the right
            Box(
                modifier = Modifier.weight(1f),
                contentAlignment = Alignment.CenterEnd
            ) {
                IconButton(onClick = onRefresh) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Refresh notifications",
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

private fun formatDate(date: Date): String {
    val formatter = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    return formatter.format(date)
}

@Preview(showBackground = true)
@Composable
fun ServiceScreenPreview() {
    AppioAppTheme {
        ServiceContent(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service for testing purposes",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            ),
            notifications = listOf(
                Notification(
                    id = "notif_1",
                    serviceId = "svc_demo_123456789",
                    serviceTitle = "Demo Service",
                    title = "Welcome to Demo Service",
                    body = "This is a sample notification to show how the notification list looks in the service screen.",
                    receivedAt = Date()
                ),
                Notification(
                    id = "notif_2",
                    serviceId = "svc_demo_123456789",
                    serviceTitle = "Demo Service",
                    title = "Another Notification",
                    body = "This is another sample notification with a longer body text that should be truncated to one line with ellipsis when it exceeds the available space.",
                    receivedAt = Date(System.currentTimeMillis() - 3600000) // 1 hour ago
                )
            ),
            onRefresh = { /* TODO */ }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ServiceScreenPreviewModePreview() {
    AppioAppTheme {
        ServiceContent(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service with preview mode enabled",
                logoURL = "https://example.com/logo.png",
                showPreview = true,
                lastUpdate = Date(),
                lastSync = Date()
            ),
            notifications = emptyList(),
            onRefresh = { /* TODO */ }
        )
    }
}

@Preview(showBackground = true, uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES)
@Composable
fun ServiceScreenDarkPreview() {
    AppioAppTheme {
        ServiceContent(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "Dark mode preview of the service screen",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            ),
            notifications = listOf(
                Notification(
                    id = "notif_1",
                    serviceId = "svc_demo_123456789",
                    serviceTitle = "Demo Service",
                    title = "Dark Mode Notification",
                    body = "This notification shows how the dark theme looks in the service screen.",
                    receivedAt = Date()
                )
            ),
            onRefresh = { /* TODO */ }
        )
    }
}
