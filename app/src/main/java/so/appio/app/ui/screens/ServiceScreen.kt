package so.appio.app.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import so.appio.app.data.entity.service.Service
import so.appio.app.ui.theme.AppioAppTheme
import java.util.Date

@Composable
fun ServiceScreen(
    modifier: Modifier = Modifier,
    service: Service,
) {
    Scaffold(modifier = modifier.fillMaxSize()) { innerPadding ->
        ServiceContent(
            service = service,
            modifier = Modifier.padding(innerPadding)
        )
    }
}

@Composable
private fun ServiceContent(
    service: Service,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Service title
        Text(
            text = service.title,
            style = MaterialTheme.typography.headlineMedium.copy(
                fontWeight = FontWeight.Bold,
                fontSize = 28.sp
            ),
            color = MaterialTheme.colorScheme.onBackground,
            textAlign = TextAlign.Center
        )
        
        // Service description (if available)
        service.description?.let { description ->
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = description,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Service ID display
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Service ID:",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = service.id,
                    style = MaterialTheme.typography.bodyLarge.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.primary,
                    textAlign = TextAlign.Center
                )
            }
        }
        
        // Preview mode indicator (if enabled)
        if (service.showPreview) {
            Spacer(modifier = Modifier.height(16.dp))
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Text(
                    text = "Preview mode",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Info text
        Text(
            text = "This is the service screen. No back navigation is available.",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ServiceScreenPreview() {
    AppioAppTheme {
        ServiceContent(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service for testing purposes",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            )
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ServiceScreenPreviewModePreview() {
    AppioAppTheme {
        ServiceContent(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "This is a demo service with preview mode enabled",
                logoURL = "https://example.com/logo.png",
                showPreview = true,
                lastUpdate = Date(),
                lastSync = Date()
            )
        )
    }
}

@Preview(showBackground = true, uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES)
@Composable
fun ServiceScreenDarkPreview() {
    AppioAppTheme {
        ServiceContent(
            service = Service(
                id = "svc_demo_123456789",
                title = "Demo Service",
                description = "Dark mode preview of the service screen",
                logoURL = "https://example.com/logo.png",
                showPreview = false,
                lastUpdate = Date(),
                lastSync = Date()
            )
        )
    }
}
